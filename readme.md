Here's an updated review and a recommended technology stack for your requirements:

---

## Updated Product Requirements

- **Native iOS and Android keyboard extensions** with companion app.
- **In-app subscription payments only** (not external website).
- **Subscription model:**
  - Example: $20/month for 1000 hours (“metered” usage).
- **Billing, subscription, and metering managed transparently within the app.**
- **All user management and status checks, usage meter, and paywall/prompt logic are inside the mobile app.**
- **App store compliance is critical.**
- **Use existing backend AI API for speech/commands.**
- **Centralized, secure storage of user, subscription, and meter data.**

---

## Platform Constraints

- **App Store & Google Play:** Apps collecting recurring payments must use the respective in-app purchase/billing SDKs.
  - **No external processors (Stripe, Paypal, or custom web flows) for in-app content.**
  - **All subscription purchases, renewals, and validations must happen through native app stores.**
- **Custom keyboard extensions cannot show payment UI or handle payment logic directly**; the companion app manages all subscription and metering.

---

## Best Technology Stack

### 1. **Frontend (Mobile/Keyboard & Companion App)**
   - **Platform:** Native per platform (required for custom keyboard functionality).
     - iOS: Swift (with “Keyboard Extension” and App Target)
     - Android: Kotlin/Java (with “InputMethodService” and App Target)
   - **UI Libraries:** Use native UIKit/SwiftUI (iOS) and Jetpack Compose (Android) for a modern experience in the companion app.
   - **In-App Billing SDKs:**
     - **iOS:** `StoreKit` for subscription purchase, renewal, and receipt validation.
     - **Android:** `Google Play Billing Library` for subscription management and usage metering.

### 2. **Backend**
   - **API Services:** Node.js or Python backend for:
     - Speech/AI API (reuse)
     - Meter data storage and validation
     - Subscription status storage (as reported from client via StoreKit/Play Billing)
   - **Database:**
     - **Recommended:** Firestore (scalable, real-time, easy mobile SDKs, usage metering support)
     - **Alternatives:** PostgreSQL (if you want advanced queries, analytics, strong relational integrity)
   - **Authentication:**
     - **Recommended:** Firebase Auth for simple sign-in (email/social)
     - **Alternative:** Custom JWT infrastructure if advanced SSO or enterprise required

### 3. **Metered Subscription Management**
   - **Usage Logging:** On every API call (or periodically from keyboard/app), log usage (e.g., speech minutes/hours) against the user in Firestore.
   - **Quota Enforcement:**
     - Backend checks if user's usage < subscription allowance for billing cycle
     - If over quota, client UI and backend both block or prompt for upgrade/renewal
   - **Reset Meters Monthly:** Automated Cloud Function or scheduled job resets used quota each month.

### 4. **In-app Subscription Verification**
   - App periodically verifies subscription status with Apple/Google servers and syncs receipts/tokens to backend.
   - Backend double-checks validity for critical API access (for fraud protection).

### 5. **Security/Compliance**
   - All sensitive info (tokens, receipts) transmitted only over HTTPS.
   - PII/usage stored only as required for service.
   - Offer clear UI/account means for subscription management (cancel, upgrade, view usage).

---

## Stack Overview Table

| Layer                 | Technology/Tool           | Notes                                                                           |
|-----------------------|--------------------------|---------------------------------------------------------------------------------|
| iOS UI                | Swift, UIKit/SwiftUI      | App + Keyboard Extension                                                         |
| Android UI            | Kotlin, Jetpack Compose   | App + Keyboard (InputMethodService)                                              |
| iOS Billing           | StoreKit                  | Native, required for App Store compliance                                        |
| Android Billing       | Google Play Billing       | Native, required for Play Store compliance                                       |
| Auth                  | Firebase Authentication   | Seamless with Firestore, supports social/email sign-in                           |
| Database              | Firebase Firestore        | Store user, subscription, meter/usage data; supports real-time updates           |
| Backend/Serverless    | Node.js/Python functions  | API glue/code, cloud functions, speech/AI endpoint proxy, meter/quota logic      |
| Server Hosting        | Firebase Functions, GCP, AWS | Scale as needed, close to Firestore/Firestore triggers                       |
| Analytics/Crash       | Firebase Analytics, Crashlytics | For monitoring app health and engagement                                 |

---

## Additional Notes

- Do not implement external web payments; all purchases must use native app store billing.
- Firestore fits well for real-time metering (simple schema: User → SubscriptionDoc, UsageDoc).
- Renewals and meter resets should be automated and robust, using serverless functions.
- Backend should verify receipts for fraud protection.
- All support, usage, and subscription management must be accessible via the companion app—not the keyboard extension.

---

## User Flow Example

1. User downloads the app, signs up (Firebase Auth), and enables the keyboard.
2. In app, user subscribes via native in-app purchase UI ($20/month for 1000 hours).
3. When user speaks, extension/app logs usage to Firestore and optionally backend for enforcement.
4. App and backend enforce monthly quota. If usage exceeds, app prompts to upgrade/renew.
5. App always displays current meter usage and billing cycle countdown.

---

**Summary:**
Use fully native app/keyboard (Swift/iOS, Kotlin/Android), store meter and user data in Firestore, manage in-app subscriptions via StoreKit/Google Play Billing, and handle metered enforcement via backend/code that interfaces with Firestore. Do NOT process subscriptions outside of the app stores to ensure compliance and best user experience.

---

**Let me know if you want this rewritten as a detailed instruction/spec for a coding AI!**